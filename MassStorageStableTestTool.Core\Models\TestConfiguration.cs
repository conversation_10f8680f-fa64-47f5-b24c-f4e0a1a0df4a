namespace MassStorageStableTestTool.Core.Models;

/// <summary>
/// 测试配置模型
/// </summary>
public class TestConfiguration
{
    /// <summary>
    /// 目标驱动器（如 "E:", "F:" 等）
    /// </summary>
    public string TargetDrive { get; set; } = string.Empty;

    /// <summary>
    /// 选中的测试工具列表
    /// </summary>
    public List<string> SelectedTools { get; set; } = new();

    /// <summary>
    /// 测试参数字典
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();

    /// <summary>
    /// 测试大小（如 "1GB", "500MB" 等）
    /// </summary>
    public string TestSize { get; set; } = "1GB";

    /// <summary>
    /// 测试次数
    /// </summary>
    public int TestCount { get; set; } = 1;

    /// <summary>
    /// 测试超时时间（秒）
    /// </summary>
    public int TimeoutSeconds { get; set; } = 3600; // 默认1小时

    /// <summary>
    /// 是否验证数据完整性
    /// </summary>
    public bool VerifyData { get; set; } = true;

    /// <summary>
    /// 是否测试全部可用空间
    /// </summary>
    public bool TestAllSpace { get; set; } = false;

    /// <summary>
    /// 块大小（如 "4K", "64K" 等）
    /// </summary>
    public string BlockSize { get; set; } = "4K";

    /// <summary>
    /// 读写比例（如 "randrw", "read", "write" 等）
    /// </summary>
    public string ReadWriteRatio { get; set; } = "randrw";

    /// <summary>
    /// 线程数
    /// </summary>
    public int ThreadCount { get; set; } = 1;

    /// <summary>
    /// 是否启用详细日志
    /// </summary>
    public bool VerboseLogging { get; set; } = false;

    /// <summary>
    /// 自定义输出目录
    /// </summary>
    public string? OutputDirectory { get; set; }

    /// <summary>
    /// 是否在测试前格式化磁盘
    /// </summary>
    public bool FormatBeforeTest { get; set; } = true;

    /// <summary>
    /// 是否使用快速格式化
    /// </summary>
    public bool QuickFormat { get; set; } = true;

    /// <summary>
    /// 配置名称
    /// </summary>
    public string ConfigurationName { get; set; } = "默认配置";

    /// <summary>
    /// 配置描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 获取指定工具的参数
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <returns>工具参数字典</returns>
    public Dictionary<string, object> GetToolParameters(string toolName)
    {
        var toolParams = new Dictionary<string, object>();
        
        foreach (var param in Parameters)
        {
            if (param.Key.StartsWith($"{toolName}.", StringComparison.OrdinalIgnoreCase))
            {
                var key = param.Key.Substring(toolName.Length + 1);
                toolParams[key] = param.Value;
            }
        }
        
        return toolParams;
    }

    /// <summary>
    /// 设置工具参数
    /// </summary>
    /// <param name="toolName">工具名称</param>
    /// <param name="parameterName">参数名称</param>
    /// <param name="value">参数值</param>
    public void SetToolParameter(string toolName, string parameterName, object value)
    {
        var key = $"{toolName}.{parameterName}";
        Parameters[key] = value;
    }

    /// <summary>
    /// 验证配置有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public (bool IsValid, List<string> Errors) Validate()
    {
        var errors = new List<string>();

        if (string.IsNullOrWhiteSpace(TargetDrive))
        {
            errors.Add("目标驱动器不能为空");
        }

        if (!SelectedTools.Any())
        {
            errors.Add("至少需要选择一个测试工具");
        }

        if (TestCount <= 0)
        {
            errors.Add("测试次数必须大于0");
        }

        if (TimeoutSeconds <= 0)
        {
            errors.Add("超时时间必须大于0");
        }

        if (ThreadCount <= 0)
        {
            errors.Add("线程数必须大于0");
        }

        return (errors.Count == 0, errors);
    }

    /// <summary>
    /// 克隆配置
    /// </summary>
    /// <returns>配置副本</returns>
    public TestConfiguration Clone()
    {
        return new TestConfiguration
        {
            TargetDrive = TargetDrive,
            SelectedTools = new List<string>(SelectedTools),
            Parameters = new Dictionary<string, object>(Parameters),
            TestSize = TestSize,
            TestCount = TestCount,
            TimeoutSeconds = TimeoutSeconds,
            VerifyData = VerifyData,
            TestAllSpace = TestAllSpace,
            BlockSize = BlockSize,
            ReadWriteRatio = ReadWriteRatio,
            ThreadCount = ThreadCount,
            VerboseLogging = VerboseLogging,
            OutputDirectory = OutputDirectory,
            FormatBeforeTest = FormatBeforeTest,
            QuickFormat = QuickFormat,
            ConfigurationName = ConfigurationName,
            Description = Description,
            CreatedAt = CreatedAt
        };
    }
}
