using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MassStorageStableTestTool.Core.Extensions;
using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Automation.Services;
using MassStorageStableTestTool.Automation.GUI;

namespace MassStorageStableTestTool.Examples;

/// <summary>
/// 磁盘格式化功能使用示例
/// </summary>
public class DiskFormatUsageExample
{
    public static async Task Main(string[] args)
    {
        // 配置依赖注入
        var services = new ServiceCollection();
        
        // 添加日志服务
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });
        
        // 添加核心服务（包括磁盘格式化服务）
        services.AddCoreServices();
        
        // 添加自动化服务
        services.AddScoped<AutomationHelper>();
        services.AddScoped<IControllerFactory, ControllerFactory>();
        
        var serviceProvider = services.BuildServiceProvider();
        
        // 示例1：直接使用磁盘格式化服务
        await DirectFormatExample(serviceProvider);
        
        // 示例2：通过测试控制器使用格式化功能
        await TestControllerFormatExample(serviceProvider);
    }
    
    /// <summary>
    /// 直接使用磁盘格式化服务的示例
    /// </summary>
    private static async Task DirectFormatExample(IServiceProvider serviceProvider)
    {
        Console.WriteLine("=== 直接使用磁盘格式化服务示例 ===");
        
        var formatService = serviceProvider.GetRequiredService<IDiskFormatService>();
        var logger = serviceProvider.GetRequiredService<ILogger<DiskFormatUsageExample>>();
        
        // 目标驱动器（请根据实际情况修改）
        string targetDrive = "E:";
        
        try
        {
            // 检查驱动器是否可以格式化
            logger.LogInformation("检查驱动器 {Drive} 是否可以格式化...", targetDrive);
            var (canFormat, issues) = await formatService.CheckDriveFormatabilityAsync(targetDrive);
            
            if (!canFormat)
            {
                logger.LogError("驱动器不能格式化: {Issues}", string.Join(", ", issues));
                return;
            }
            
            logger.LogInformation("驱动器检查通过，可以进行格式化");
            
            // 创建进度报告器
            var progress = new Progress<ProgressEventArgs>(args =>
            {
                Console.WriteLine($"格式化进度: {args.Progress}% - {args.Status}");
            });
            
            // 执行格式化（使用驱动器当前的文件系统和卷标）
            logger.LogInformation("开始格式化驱动器...");
            var result = await formatService.FormatDriveAsync(
                targetDrive,
                fileSystem: null, // 使用当前文件系统
                volumeLabel: null, // 使用当前卷标
                quickFormat: true, // 快速格式化
                CancellationToken.None,
                progress);
            
            if (result.Success)
            {
                logger.LogInformation("格式化成功！");
                logger.LogInformation("文件系统: {FileSystem}", result.FileSystem);
                logger.LogInformation("卷标: {VolumeLabel}", result.VolumeLabel);
                logger.LogInformation("耗时: {Duration}", result.Duration);
            }
            else
            {
                logger.LogError("格式化失败: {Error}", result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "格式化过程中出现异常");
        }
    }
    
    /// <summary>
    /// 通过测试控制器使用格式化功能的示例
    /// </summary>
    private static async Task TestControllerFormatExample(IServiceProvider serviceProvider)
    {
        Console.WriteLine("\n=== 通过测试控制器使用格式化功能示例 ===");
        
        var controllerFactory = serviceProvider.GetRequiredService<IControllerFactory>();
        var logger = serviceProvider.GetRequiredService<ILogger<DiskFormatUsageExample>>();
        
        try
        {
            // 创建H2testw控制器
            var controller = await controllerFactory.CreateControllerAsync("H2testw");
            if (controller == null)
            {
                logger.LogError("无法创建H2testw控制器");
                return;
            }
            
            // 创建测试配置，启用格式化
            var testConfig = new TestConfiguration
            {
                TargetDrive = "E:", // 请根据实际情况修改
                SelectedTools = new List<string> { "H2testw" },
                FormatBeforeTest = true, // 启用测试前格式化（使用驱动器当前的文件系统和卷标）
                QuickFormat = true,
                TestAllSpace = false,
                TestSize = "100MB", // 小测试，仅用于演示
                ConfigurationName = "格式化测试示例"
            };
            
            // 创建进度报告器
            var progress = new Progress<ProgressEventArgs>(args =>
            {
                Console.WriteLine($"测试进度: {args.Progress}% - {args.Status}");
            });
            
            logger.LogInformation("开始执行测试（包含格式化）...");
            
            // 执行测试（会自动在测试前格式化磁盘）
            var result = await controller.ExecuteTestAsync(testConfig, CancellationToken.None, progress);
            
            if (result.Success)
            {
                logger.LogInformation("测试执行成功！");
                logger.LogInformation("测试工具: {ToolName}", result.ToolName);
                logger.LogInformation("开始时间: {StartTime}", result.StartTime);
                logger.LogInformation("结束时间: {EndTime}", result.EndTime);
                logger.LogInformation("总耗时: {Duration}", result.Duration);
            }
            else
            {
                logger.LogError("测试执行失败: {Error}", result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "测试执行过程中出现异常");
        }
    }
}

/// <summary>
/// 配置示例
/// </summary>
public class ConfigurationExample
{
    /// <summary>
    /// 创建启用格式化的测试配置示例
    /// </summary>
    public static TestConfiguration CreateFormatEnabledConfig()
    {
        return new TestConfiguration
        {
            TargetDrive = "E:",
            SelectedTools = new List<string> { "H2testw" },

            // 格式化配置（使用驱动器当前的文件系统和卷标）
            FormatBeforeTest = true,        // 启用测试前格式化
            QuickFormat = true,             // 使用快速格式化

            // 测试配置
            TestAllSpace = true,            // 测试全部空间
            VerifyData = true,              // 验证数据完整性
            TestCount = 1,                  // 测试次数
            TimeoutSeconds = 3600,          // 超时时间（1小时）

            ConfigurationName = "完整格式化测试",
            Description = "在测试前格式化磁盘（保持原有文件系统和卷标），然后执行完整的H2testw测试"
        };
    }
    
    /// <summary>
    /// 创建禁用格式化的测试配置示例
    /// </summary>
    public static TestConfiguration CreateFormatDisabledConfig()
    {
        return new TestConfiguration
        {
            TargetDrive = "E:",
            SelectedTools = new List<string> { "H2testw" },
            
            // 禁用格式化
            FormatBeforeTest = false,
            
            // 测试配置
            TestAllSpace = false,
            TestSize = "1GB",
            VerifyData = true,
            TestCount = 3,
            
            ConfigurationName = "快速测试（无格式化）",
            Description = "跳过格式化步骤，直接执行测试"
        };
    }
}
