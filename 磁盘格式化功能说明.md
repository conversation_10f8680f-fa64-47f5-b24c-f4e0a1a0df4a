# 磁盘格式化功能说明

## 概述

本项目已集成磁盘格式化功能，可以在每个测试项目执行之前自动格式化目标磁盘。这确保了测试环境的一致性和可靠性。

## 功能特性

- ✅ **自动格式化**: 在测试执行前自动格式化目标驱动器
- ✅ **多文件系统支持**: 支持 FAT32、NTFS、exFAT 文件系统
- ✅ **安全检查**: 只允许格式化可移动驱动器，防止误操作
- ✅ **进度反馈**: 提供格式化进度的实时反馈
- ✅ **配置灵活**: 可通过配置启用/禁用格式化功能
- ✅ **错误处理**: 完善的错误处理和日志记录

## 配置选项

在 `TestConfiguration` 中新增了以下格式化相关配置：

```csharp
public class TestConfiguration
{
    /// <summary>
    /// 是否在测试前格式化磁盘
    /// </summary>
    public bool FormatBeforeTest { get; set; } = true;

    /// <summary>
    /// 是否使用快速格式化
    /// </summary>
    public bool QuickFormat { get; set; } = true;
}
```

**注意**: 格式化时会自动使用驱动器当前的文件系统类型和卷标名称，无需额外配置。

## 使用方法

### 1. 启用格式化功能

```csharp
var testConfig = new TestConfiguration
{
    TargetDrive = "E:",
    SelectedTools = new List<string> { "H2testw" },

    // 启用格式化（使用驱动器当前的文件系统和卷标）
    FormatBeforeTest = true,
    QuickFormat = true
};
```

### 2. 禁用格式化功能

```csharp
var testConfig = new TestConfiguration
{
    TargetDrive = "E:",
    SelectedTools = new List<string> { "H2testw" },
    
    // 禁用格式化
    FormatBeforeTest = false
};
```

### 3. 依赖注入配置

在应用程序启动时注册磁盘格式化服务：

```csharp
// Program.cs 或 Startup.cs
services.AddCoreServices(); // 包含磁盘格式化服务

// 或者单独注册
services.AddDiskFormatService();
```

## 安全机制

为了防止意外格式化重要数据，系统实施了以下安全检查：

1. **驱动器类型检查**: 只允许格式化可移动驱动器（DriveType.Removable）
2. **系统驱动器保护**: 禁止格式化系统驱动器
3. **驱动器占用检查**: 检查驱动器是否被其他程序占用
4. **用户确认**: 格式化前会进行安全性检查

## 支持的文件系统

格式化时会自动保持驱动器当前的文件系统类型：

- **FAT32**: 兼容性最好，适用于大多数设备
- **NTFS**: Windows原生文件系统，支持大文件
- **exFAT**: 适用于大容量存储设备

**注意**: 系统会检测并保持驱动器原有的文件系统类型，确保兼容性。

## 错误处理

格式化过程中可能遇到的错误及处理方式：

| 错误类型 | 描述 | 解决方案 |
|---------|------|---------|
| 驱动器不存在 | 目标驱动器不可访问 | 检查驱动器连接 |
| 驱动器被占用 | 其他程序正在使用驱动器 | 关闭相关程序 |
| 权限不足 | 没有格式化权限 | 以管理员身份运行 |
| 不支持的文件系统 | 指定的文件系统不受支持 | 使用支持的文件系统 |

## 日志记录

格式化过程会产生详细的日志记录：

```
[2024-01-15 10:30:00] 开始格式化驱动器 E:
[2024-01-15 10:30:01] 检查驱动器可格式化性...
[2024-01-15 10:30:02] 格式化进度: 25% - 正在格式化...
[2024-01-15 10:30:05] 格式化进度: 100% - 格式化完成
[2024-01-15 10:30:06] 格式化成功，文件系统: FAT32，卷标: TEST_DRIVE
```

## 性能考虑

- **快速格式化**: 默认启用，大幅减少格式化时间
- **并发控制**: 同时只能进行一个格式化操作
- **超时机制**: 防止格式化操作无限期等待

## 示例代码

完整的使用示例请参考 `Example_DiskFormatUsage.cs` 文件。

## 注意事项

⚠️ **重要警告**:
- 格式化会删除驱动器上的所有数据
- 请确保已备份重要数据
- 只在测试环境中使用此功能
- 建议使用专用的测试设备

## 故障排除

### 格式化失败
1. 检查驱动器是否正确连接
2. 确认以管理员权限运行程序
3. 检查驱动器是否被其他程序占用
4. 查看详细错误日志

### 格式化后测试失败
1. 等待系统完全识别格式化后的驱动器
2. 检查驱动器是否正常挂载
3. 验证文件系统是否正确

## 更新日志

- **v1.0.0**: 初始版本，支持基本格式化功能
- **v1.1.0**: 添加进度反馈和错误处理
- **v1.2.0**: 增强安全检查机制
