using FlaUI.Core;
using FlaUI.Core.AutomationElements;
using FlaUI.Core.Input;
using FlaUI.Core.WindowsAPI;
using FlaUI.Core.Patterns;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using System.Drawing;
using FlaUI.Core.Tools;
using FlaUI.Core.Exceptions;

namespace MassStorageStableTestTool.Automation.GUI;

/// <summary>
/// 输入模拟器，负责模拟鼠标点击、键盘输入等用户交互
/// </summary>
public class InputSimulator
{
    private readonly ILogger<InputSimulator> _logger;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public InputSimulator(ILogger<InputSimulator> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 点击UI元素
    /// </summary>
    /// <param name="element">要点击的UI元素</param>
    /// <param name="doubleClick">是否执行双击操作</param>
    /// <returns>是否成功点击</returns>
    public async Task<bool> ClickElement(AutomationElement element, bool doubleClick = false, TimeSpan? timeout = null)
    {
        if (element == null)
            throw new ArgumentNullException(nameof(element));

        try
        {
            // 确保元素可见和可用
            if (!IsElementStableAndVisible(element))
            {
                var elementName = GetSafeElementName(element);
                _logger.LogWarning($"元素 '{elementName}' 不可用或不可见，无法点击");
                return false;
            }

            //设置焦点
            element.Focus();
            var focusElementName = GetSafeElementName(element);
            _logger.LogDebug($"聚焦元素 '{focusElementName}'");

            if (doubleClick)
            {
                element.DoubleClick();
                _logger.LogDebug("执行双击操作");
            }
            else
            {
                element.Click();
                _logger.LogDebug("执行单击操作");
            }
            //等待30毫秒
            await Task.Delay(30);

            // 等待点击操作完成
            return await WaitForClickCompletionAsync(element, timeout ?? TimeSpan.FromMilliseconds(500));
        }
        catch (Exception ex)
        {
            var elementName = GetSafeElementName(element);
            _logger.LogError($"点击元素 '{elementName}' 失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 等待元素稳定（可用且可见）
    /// </summary>
    /// <param name="element">目标元素</param>
    /// <param name="timeout">超时时间</param>
    /// <returns>是否稳定</returns>
    private async Task<bool> WaitUntilElementStableAsync(AutomationElement element, TimeSpan timeout)
    {
        var endTime = DateTime.Now + timeout;
        while (DateTime.Now < endTime)
        {
            try
            {
                // 安全地检查元素状态
                if (IsElementStableAndVisible(element))
                    return true;
            }
            catch (Exception ex)
            {
                _logger.LogDebug($"等待元素稳定时出现异常: {ex.Message}");
            }
            await Task.Delay(100);
        }
        var elementName = GetSafeElementName(element);
        _logger.LogWarning($"等待元素 '{elementName}' 稳定超时");
        return false;
    }

    /// <summary>
    /// 等待点击操作完成
    /// </summary>
    /// <param name="element">被点击的元素</param>
    /// <param name="timeout">超时时间</param>
    /// <returns>操作是否成功完成</returns>
    private async Task<bool> WaitForClickCompletionAsync(AutomationElement element, TimeSpan timeout)
    {
        var endTime = DateTime.Now + timeout;
        var elementName = GetSafeElementName(element);

        // 给点击操作一些时间来执行
        await Task.Delay(100);

        while (DateTime.Now < endTime)
        {
            try
            {
                // 检查元素是否仍然存在且可访问
                bool elementExists = IsElementAccessible(element);

                if (elementExists)
                {
                    // 元素仍然存在，检查是否稳定
                    if (IsElementStableAndVisible(element))
                    {
                        _logger.LogDebug($"点击操作完成，元素 '{elementName}' 保持稳定");
                        return true;
                    }
                }
                else
                {
                    // 元素已经消失（比如窗口关闭），这通常意味着操作成功
                    _logger.LogDebug($"点击操作完成，元素 '{elementName}' 已消失（可能是窗口关闭）");
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug($"等待点击完成时出现异常: {ex.Message}");
                // 如果访问元素时出现异常，可能是元素已经被销毁，这通常是成功的标志
                _logger.LogDebug($"点击操作可能已完成，元素 '{elementName}' 不再可访问");
                return true;
            }

            await Task.Delay(50);
        }

        _logger.LogWarning($"等待点击操作完成超时，元素: '{elementName}'");
        return false;
    }

    /// <summary>
    /// 检查元素是否可访问（不抛出异常）
    /// </summary>
    /// <param name="element">要检查的元素</param>
    /// <returns>元素是否可访问</returns>
    private bool IsElementAccessible(AutomationElement element)
    {
       try
        {
            // 检查基本属性
            _ = element.BoundingRectangle;
            _ = element.Properties.ControlType.Value;
            
            // 检查元素是否启用（如果支持）
            if (element.Properties.IsEnabled.IsSupported)
            {
                if (!element.Properties.IsEnabled.TryGetValue(out bool enabled) || !enabled)
                    return false;
            }
            
            return true;
        }
        catch (ElementNotAvailableException)
        {
            return false;
        }
    }

    /// <summary>
    /// 右键点击 UI 元素
    /// </summary>
    /// <param name="element">要右键点击的 UI 元素</param>
    /// <returns>是否成功右键点击</returns>
    public async Task<bool> RightClickElement(AutomationElement element)
    {
        if (element == null)
            throw new ArgumentNullException(nameof(element));

        try
        {
            // 确保元素可用且可见
            if (!IsElementStableAndVisible(element))
            {
                var elementName = GetSafeElementName(element);
                _logger.LogWarning($"元素 '{elementName}' 不可用或不可见，无法右键点击");
                return false;
            }

            // 设置焦点并滚动到可见区域
            element.Focus();
            var focusElementName = GetSafeElementName(element);
            _logger.LogDebug($"聚焦并滚动到元素 '{focusElementName}'");

            element.RightClick();
            var clickElementName = GetSafeElementName(element);
            _logger.LogDebug($"右键点击元素 '{clickElementName}'");

            // 等待界面稳定（例如上下文菜单弹出）
            await WaitUntilElementStableAsync(element, TimeSpan.FromMilliseconds(500));

            return true;
        }
        catch (Exception ex)
        {
            var elementName = GetSafeElementName(element);
            _logger.LogError($"右键点击元素 '{elementName}' 失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 在指定坐标点击
    /// </summary>
    /// <param name="x">X 坐标</param>
    /// <param name="y">Y 坐标</param>
    /// <param name="doubleClick">是否双击</param>
    /// <returns>是否成功点击</returns>
    public async Task<bool> ClickAt(int x, int y, bool doubleClick = false)
    {
        try
        {
            // 验证坐标有效性
            if (Screen.PrimaryScreen == null)
            {
                _logger.LogError("无法获取主屏幕信息，Screen.PrimaryScreen 为 null");
                return false;
            }
            var screenBounds = Screen.PrimaryScreen.Bounds;
            if (x < 0 || x > screenBounds.Width || y < 0 || y > screenBounds.Height)
            {
                _logger.LogWarning($"坐标 ({x}, {y}) 超出屏幕范围 ({screenBounds.Width}, {screenBounds.Height})");
                return false;
            }

            var point = new Point(x, y);
            _logger.LogDebug($"在坐标 ({x}, {y}) {(doubleClick ? "双击" : "单击")}");

            if (doubleClick)
            {
                Mouse.DoubleClick(point);
                _logger.LogDebug("执行双击操作");
            }
            else
            {
                Mouse.Click(point);
                _logger.LogDebug("执行单击操作");
            }

            // 等待界面稳定
            await Task.Delay(500); // 可调整为更具体的条件等待

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError($"在坐标 ({x}, {y}) {(doubleClick ? "双击" : "单击")} 失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 向元素输入文本
    /// </summary>
    /// <param name="element">目标元素</param>
    /// <param name="text">要输入的文本</param>
    /// <param name="clearFirst">是否先清空现有内容</param>
    /// <returns>是否成功输入</returns>
    public bool TypeText(AutomationElement element, string text, bool clearFirst = true)
    {
        if (element == null)
            throw new ArgumentNullException(nameof(element));
        if (text == null)
            text = string.Empty;

        try
        {
            if (!element.IsAvailable)
            {
                _logger.LogWarning("元素不可用，无法输入文本");
                return false;
            }

            // 先点击元素获得焦点
            element.Focus();
            _logger.LogDebug($"聚焦元素 '{element.Name}'");

            if (clearFirst)
            {
                // 全选并删除现有内容
                Keyboard.TypeSimultaneously(VirtualKeyShort.CONTROL, VirtualKeyShort.KEY_A);
                Retry.WhileException(() => Keyboard.Type(VirtualKeyShort.DELETE), TimeSpan.FromMilliseconds(1500), TimeSpan.FromMilliseconds(500), true, text);
            }

            if (!string.IsNullOrEmpty(text))
            {
                _logger.LogDebug($"向元素 '{element.Name}' 输入文本: '{text}'");
                // // 尝试使用ValuePattern直接设置值
                // var valuePattern = element.Patterns.Value.PatternOrDefault;
                // if (valuePattern != null)
                // {
                //     valuePattern.SetValue(text);
                // }
                // else
                // {
                //     // 如果不支持ValuePattern，则使用键盘输入，但需要确保焦点
                //     element.Focus();
                //     Keyboard.Type(text);
                // }
                Keyboard.Type(text);
                Thread.Sleep(200);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError($"向元素 '{element.Name}' 输入文本失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 发送按键
    /// </summary>
    /// <param name="key">要发送的按键</param>
    /// <returns>是否成功发送</returns>
    public bool SendKey(VirtualKeyShort key)
    {
        try
        {
            _logger.LogDebug($"发送按键: {key}");
            Keyboard.Type(key);
            Thread.Sleep(100);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError($"发送按键 {key} 失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 发送组合键
    /// </summary>
    /// <param name="keys">要同时按下的按键</param>
    /// <returns>是否成功发送</returns>
    public bool SendKeysCombination(params VirtualKeyShort[] keys)
    {
        if (keys == null || keys.Length == 0)
            throw new ArgumentException("按键组合不能为空", nameof(keys));

        try
        {
            _logger.LogDebug($"发送组合键: {string.Join("+", keys)}");
            Keyboard.TypeSimultaneously(keys);
            Thread.Sleep(100);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError($"发送组合键失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 拖拽元素
    /// </summary>
    /// <param name="sourceElement">源元素</param>
    /// <param name="targetElement">目标元素</param>
    /// <returns>是否成功拖拽</returns>
    public bool DragAndDrop(AutomationElement sourceElement, AutomationElement targetElement)
    {
        if (sourceElement == null)
            throw new ArgumentNullException(nameof(sourceElement));
        if (targetElement == null)
            throw new ArgumentNullException(nameof(targetElement));

        try
        {
            if (!sourceElement.IsAvailable || !targetElement.IsAvailable)
            {
                _logger.LogWarning("源元素或目标元素不可用，无法拖拽");
                return false;
            }

            var sourceRect = sourceElement.BoundingRectangle;
            var targetRect = targetElement.BoundingRectangle;
            var sourcePoint = sourceRect.Center();
            var targetPoint = targetRect.Center();

            _logger.LogDebug($"拖拽元素从 ({sourcePoint.X}, {sourcePoint.Y}) 到 ({targetPoint.X}, {targetPoint.Y})");

            Mouse.DragHorizontally(sourcePoint, targetPoint.X - sourcePoint.X);
            Thread.Sleep(500);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError($"拖拽操作失败: {ex.Message}");
            return false;
        }
    }

   /// <summary>
    /// 滚动鼠标滚轮到指定元素（异步）
    /// </summary>
    /// <param name="element">目标自动化元素</param>
    /// <param name="delta">滚动量（正数向上，负数向下，单位为滚动单位，推荐120的倍数）</param>
    /// <param name="retryCount">重试次数（默认3次）</param>
    /// <returns>是否成功滚动</returns>
    public async Task<bool> ScrollWheelAsync(AutomationElement element, int delta, int retryCount = 3)
    {
        if (element == null)
        {
            _logger.LogError("目标元素为 null");
            throw new ArgumentNullException(nameof(element));
        }

        try
        {
            // 验证元素可用性
            if (!element.IsEnabled || element.IsOffscreen)
            {
                _logger.LogWarning($"元素 '{element.Name ?? element.AutomationId}' 不可用或不在屏幕上，无法滚动");
                return false;
            }

            // 获取元素中心点
            var boundingRect = element.BoundingRectangle;
            if (boundingRect.IsEmpty)
            {
                _logger.LogWarning($"元素 '{element.Name ?? element.AutomationId}' 的边界矩形无效");
                return false;
            }
            var centerPoint = boundingRect.Center();

            _logger.LogDebug($"在元素 '{element.Name ?? element.AutomationId}' ({centerPoint.X}, {centerPoint.Y}) 滚动鼠标滚轮，滚动量: {delta}");

            // 尝试使用 Retry.WhileFalseAsync
            bool success;
            
            success = await CustomRetryAsync(
                async () =>
                {
                    Mouse.MoveTo(centerPoint);
                    Mouse.Scroll(delta);
                    await Task.Delay(100);
                    return await VerifyScrollAsync(element, delta);
                },
                TimeSpan.FromSeconds(1),
                retryCount);
        

            if (!success)
            {
                _logger.LogWarning($"滚动元素 '{element.Name ?? element.AutomationId}' 失败，经过 {retryCount} 次重试");
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"滚动鼠标滚轮失败，元素: '{element.Name ?? element.AutomationId}'，滚动量: {delta}");
            return false;
        }
    }

    /// <summary>
    /// 自定义异步重试逻辑
    /// </summary>
    private async Task<bool> CustomRetryAsync(Func<Task<bool>> action, TimeSpan interval, int retryCount)
    {
        for (int i = 0; i < retryCount; i++)
        {
            if (await action())
                return true;
            _logger.LogDebug($"滚动重试 {i + 1}/{retryCount}");
            await Task.Delay(interval);
        }
        return false;
    }

    /// <summary>
    /// 验证滚动是否成功（异步）
    /// </summary>
    /// <param name="element">目标元素</param>
    /// <param name="delta">预期滚动量</param>
    /// <returns>是否滚动成功</returns>
    private async Task<bool> VerifyScrollAsync(AutomationElement element, int delta)
    {
        try
        {
            // 检查滚动条位置（如果支持 ScrollPattern）
            var scrollPattern = element.Patterns.Scroll.PatternOrDefault;
            if (scrollPattern != null)
            {
                double previousVerticalPercent = scrollPattern.VerticalScrollPercent;
                await Task.Delay(50); // 等待 UI 响应
                double currentVerticalPercent = scrollPattern.VerticalScrollPercent;
                bool scrolled = delta > 0
                    ? currentVerticalPercent > previousVerticalPercent
                    : currentVerticalPercent < previousVerticalPercent;

                _logger.LogDebug($"滚动验证: 垂直滚动百分比从 {previousVerticalPercent:F2}% 变为 {currentVerticalPercent:F2}%");
                return scrolled;
            }

            // 回退检查：元素是否仍可见
            bool isStillVisible = !element.IsOffscreen;
            _logger.LogDebug($"滚动验证: 元素 '{element.Name ?? element.AutomationId}' {(isStillVisible ? "仍可见" : "不可见")}");
            return isStillVisible;
        }
        catch (Exception ex)
        {
            _logger.LogWarning($"滚动验证失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 等待指定时间
    /// </summary>
    /// <param name="milliseconds">等待时间（毫秒）</param>
    public void Wait(int milliseconds)
    {
        if (milliseconds > 0)
        {
            _logger.LogDebug($"等待 {milliseconds} 毫秒");
            Thread.Sleep(milliseconds);
        }
    }

    /// <summary>
    /// 模拟按住按键
    /// </summary>
    /// <param name="key">要按住的按键</param>
    /// <param name="duration">按住时长（毫秒）</param>
    /// <returns>是否成功</returns>
    public bool HoldKey(VirtualKeyShort key, int duration)
    {
        try
        {
            _logger.LogDebug($"按住按键 {key} 持续 {duration} 毫秒");
            Keyboard.Press(key);
            Thread.Sleep(duration);
            Keyboard.Release(key);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError($"按住按键 {key} 失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 安全地检查元素是否稳定且可见
    /// </summary>
    /// <param name="element">UI元素</param>
    /// <returns>元素是否稳定且可见</returns>
    private bool IsElementStableAndVisible(AutomationElement element)
    {
        try
        {
            // 检查元素是否启用
            bool isEnabled = true;
            if (element.Properties.IsEnabled.IsSupported)
            {
                isEnabled = element.Properties.IsEnabled.TryGetValue(out bool enabled) && enabled;
            }

            // 检查元素是否在屏幕上（不是离屏）
            bool isOnScreen = true;
            if (element.Properties.IsOffscreen.IsSupported)
            {
                isOnScreen = !element.Properties.IsOffscreen.TryGetValue(out bool offscreen) || !offscreen;
            }

            // 检查元素是否可用
            bool isAvailable = true;
            try
            {
                // 尝试访问元素的基本属性来验证可用性
                _ = element.BoundingRectangle;
            }
            catch
            {
                isAvailable = false;
            }

            return isEnabled && isOnScreen && isAvailable;
        }
        catch (Exception ex)
        {
            _logger.LogDebug($"检查元素稳定性时出现异常: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 安全地获取元素名称，避免属性不支持异常
    /// </summary>
    /// <param name="element">UI元素</param>
    /// <returns>元素名称或替代标识</returns>
    private string GetSafeElementName(AutomationElement element)
    {
        try
        {
            // 尝试获取 Name 属性
            if (element.Properties.Name.IsSupported)
            {
                var name = element.Properties.Name.Value;
                if (!string.IsNullOrEmpty(name))
                    return name;
            }
        }
        catch
        {
            // Name 属性访问失败，继续尝试其他属性
        }

        try
        {
            // 尝试获取 AutomationId
            if (element.Properties.AutomationId.IsSupported)
            {
                var automationId = element.Properties.AutomationId.Value;
                if (!string.IsNullOrEmpty(automationId))
                    return $"[AutomationId: {automationId}]";
            }
        }
        catch
        {
            // AutomationId 属性访问失败，继续尝试其他属性
        }

        try
        {
            // 尝试获取 ClassName
            if (element.Properties.ClassName.IsSupported)
            {
                var className = element.Properties.ClassName.Value;
                if (!string.IsNullOrEmpty(className))
                    return $"[ClassName: {className}]";
            }
        }
        catch
        {
            // ClassName 属性访问失败
        }

        try
        {
            // 尝试获取 ControlType
            if (element.Properties.ControlType.IsSupported)
            {
                var controlType = element.Properties.ControlType.Value;
                return $"[ControlType: {controlType}]";
            }
        }
        catch
        {
            // ControlType 属性访问失败
        }

        // 所有属性都无法访问，返回默认标识
        return "[未知元素]";
    }
}
