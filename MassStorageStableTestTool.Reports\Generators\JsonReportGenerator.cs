using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Enums;
using MassStorageStableTestTool.Reports.Common;
using MassStorageStableTestTool.Reports.Models;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace MassStorageStableTestTool.Reports.Generators;

/// <summary>
/// JSON格式报告生成器
/// </summary>
public class JsonReportGenerator : BaseReportGenerator
{
    private readonly JsonSerializerOptions _jsonOptions;

    /// <summary>
    /// 支持的报告格式
    /// </summary>
    public override ReportFormat SupportedFormat => ReportFormat.JSON;

    /// <summary>
    /// 生成器名称
    /// </summary>
    public override string GeneratorName => "JSON Report Generator";

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="configuration">报告配置</param>
    public JsonReportGenerator(ILogger<JsonReportGenerator> logger, ReportGenerationConfiguration? configuration = null)
        : base(logger, configuration)
    {
        _jsonOptions = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            Converters = { new JsonStringEnumConverter() }
        };
    }

    /// <summary>
    /// 生成报告内容
    /// </summary>
    /// <param name="reportData">报告数据</param>
    /// <param name="template">模板内容</param>
    /// <returns>报告内容</returns>
    protected override async Task<string> GenerateReportContentAsync(object reportData, string template)
    {
        try
        {
            // 从reportData中提取TestSuiteResult
            var testSuiteResult = ExtractTestSuiteResult(reportData);
            if (testSuiteResult == null)
            {
                return await GenerateSimpleJsonReportAsync();
            }

            // 构建JSON报告数据结构
            var jsonReportData = BuildJsonReportData(testSuiteResult);
            
            return JsonSerializer.Serialize(jsonReportData, _jsonOptions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "生成JSON报告时出错");
            return await GenerateSimpleJsonReportAsync();
        }
    }

    /// <summary>
    /// 获取默认文件扩展名
    /// </summary>
    /// <returns>文件扩展名</returns>
    public override string GetDefaultFileExtension() => ".json";

    /// <summary>
    /// 验证报告内容
    /// </summary>
    /// <param name="reportContent">报告内容</param>
    /// <returns>错误列表</returns>
    protected override List<string> ValidateReportContent(string reportContent)
    {
        var errors = new List<string>();

        try
        {
            JsonDocument.Parse(reportContent);
        }
        catch (JsonException ex)
        {
            errors.Add($"JSON格式无效: {ex.Message}");
        }

        return errors;
    }

    /// <summary>
    /// 获取后备模板
    /// </summary>
    /// <returns>后备模板内容</returns>
    protected override string GetFallbackTemplate()
    {
        // JSON格式不使用模板
        return string.Empty;
    }

    /// <summary>
    /// 从报告数据中提取TestSuiteResult
    /// </summary>
    /// <param name="reportData">报告数据</param>
    /// <returns>TestSuiteResult或null</returns>
    private TestSuiteResult? ExtractTestSuiteResult(object reportData)
    {
        try
        {
            var type = reportData.GetType();
            var testSuiteProperty = type.GetProperty("TestSuite");
            return testSuiteProperty?.GetValue(reportData) as TestSuiteResult;
        }
        catch
        {
            return null;
        }
    }

    /// <summary>
    /// 构建JSON报告数据结构
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <returns>JSON报告数据</returns>
    private object BuildJsonReportData(TestSuiteResult testSuiteResult)
    {
        return new
        {
            ReportMetadata = new
            {
                Title = _configuration.Title,
                GeneratedAt = DateTime.Now,
                GeneratedBy = GeneratorName,
                Version = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version?.ToString() ?? "1.0.0",
                Author = _configuration.Author,
                Organization = _configuration.Organization
            },
            TestOverview = new
            {
                TargetDrive = testSuiteResult.Configuration.TargetDrive,
                StartTime = testSuiteResult.StartTime,
                EndTime = testSuiteResult.EndTime,
                Duration = new
                {
                    TotalMinutes = Math.Round(testSuiteResult.Duration.TotalMinutes, 2),
                    TotalSeconds = Math.Round(testSuiteResult.Duration.TotalSeconds, 2),
                    TotalHours = Math.Round(testSuiteResult.Duration.TotalHours, 2)
                },
                Status = testSuiteResult.Status,
                SelectedTools = testSuiteResult.Configuration.SelectedTools
            },
            TestSummary = new
            {
                TotalTests = testSuiteResult.TotalTestsCount,
                SuccessfulTests = testSuiteResult.SuccessfulTestsCount,
                FailedTests = testSuiteResult.FailedTestsCount,
                SuccessRate = Math.Round(testSuiteResult.SuccessRate, 2),
                AllTestsPassed = testSuiteResult.AllTestsPassed
            },
            TestResults = testSuiteResult.TestResults.Select(result => new
            {
                ToolName = result.ToolName,
                Status = result.Status,
                Success = result.Success,
                StartTime = result.StartTime,
                EndTime = result.EndTime,
                Duration = new
                {
                    TotalMinutes = Math.Round(result.Duration.TotalMinutes, 2),
                    TotalSeconds = Math.Round(result.Duration.TotalSeconds, 2)
                },
                Performance = result.Performance != null ? new
                {
                    ReadSpeed = result.Performance.ReadSpeed,
                    WriteSpeed = result.Performance.WriteSpeed,
                    ReadIOPS = result.Performance.ReadIOPS,
                    WriteIOPS = result.Performance.WriteIOPS,
                    ReadLatency = result.Performance.ReadLatency,
                    WriteLatency = result.Performance.WriteLatency,
                    AverageLatency = result.Performance.AverageLatency,
                    MinLatency = result.Performance.MinLatency,
                    MaxLatency = result.Performance.MaxLatency
                } : null,
                ErrorMessage = result.ErrorMessage,
                Warnings = result.Warnings,
                Data = result.Data,
                OutputFiles = result.OutputFiles,
                LogEntries = _configuration.IncludeDetailedLogs ? result.Logs : null
            }),
            SystemInformation = testSuiteResult.SystemInfo != null ? new
            {
                OperatingSystem = testSuiteResult.SystemInfo.OperatingSystem,
                Processor = testSuiteResult.SystemInfo.Processor,
                TotalMemoryGB = Math.Round(testSuiteResult.SystemInfo.TotalMemory, 2),
                AvailableMemoryGB = Math.Round(testSuiteResult.SystemInfo.AvailableMemory, 2),
                Architecture = testSuiteResult.SystemInfo.Architecture,
                MachineName = testSuiteResult.SystemInfo.ComputerName,
                UserName = testSuiteResult.SystemInfo.UserName,
                DotNetVersion = testSuiteResult.SystemInfo.DotNetVersion
            } : null,
            DriveInformation = testSuiteResult.DriveInfo != null ? new
            {
                Name = testSuiteResult.DriveInfo.Name,
                Label = testSuiteResult.DriveInfo.Label,
                FileSystem = testSuiteResult.DriveInfo.FileSystem,
                DriveType = testSuiteResult.DriveInfo.DriveType,
                TotalSizeGB = Math.Round(testSuiteResult.DriveInfo.TotalSize / (1024.0 * 1024.0 * 1024.0), 2),
                AvailableFreeSpaceGB = Math.Round(testSuiteResult.DriveInfo.AvailableFreeSpace / (1024.0 * 1024.0 * 1024.0), 2),
                UsedSpaceGB = Math.Round(testSuiteResult.DriveInfo.UsedSpace / (1024.0 * 1024.0 * 1024.0), 2),
                UsagePercentage = Math.Round(testSuiteResult.DriveInfo.UsagePercentage, 2),
                IsReady = testSuiteResult.DriveInfo.IsReady
            } : null,
            PerformanceSummary = CalculatePerformanceSummary(testSuiteResult),
            Configuration = new
            {
                TestAllSpace = testSuiteResult.Configuration.TestAllSpace,
                VerifyData = testSuiteResult.Configuration.VerifyData,
                TestSize = testSuiteResult.Configuration.TestSize,
                TimeoutSeconds = testSuiteResult.Configuration.TimeoutSeconds,
                Parameters = testSuiteResult.Configuration.Parameters
            },
            Warnings = testSuiteResult.Warnings,
            Logs = _configuration.IncludeDetailedLogs ? testSuiteResult.Logs : null
        };
    }

    /// <summary>
    /// 计算性能摘要
    /// </summary>
    /// <param name="testSuiteResult">测试套件结果</param>
    /// <returns>性能摘要</returns>
    private object? CalculatePerformanceSummary(TestSuiteResult testSuiteResult)
    {
        var performanceResults = testSuiteResult.TestResults
            .Where(r => r.Performance != null)
            .Select(r => r.Performance!)
            .ToList();

        if (!performanceResults.Any())
            return null;

        return new
        {
            ReadSpeed = new
            {
                Average = performanceResults.Where(p => p.ReadSpeed.HasValue).Average(p => p.ReadSpeed!.Value),
                Min = performanceResults.Where(p => p.ReadSpeed.HasValue).Min(p => p.ReadSpeed!.Value),
                Max = performanceResults.Where(p => p.ReadSpeed.HasValue).Max(p => p.ReadSpeed!.Value)
            },
            WriteSpeed = new
            {
                Average = performanceResults.Where(p => p.WriteSpeed.HasValue).Average(p => p.WriteSpeed!.Value),
                Min = performanceResults.Where(p => p.WriteSpeed.HasValue).Min(p => p.WriteSpeed!.Value),
                Max = performanceResults.Where(p => p.WriteSpeed.HasValue).Max(p => p.WriteSpeed!.Value)
            },
            ReadIOPS = new
            {
                Average = performanceResults.Where(p => p.ReadIOPS.HasValue).Average(p => p.ReadIOPS!.Value),
                Min = performanceResults.Where(p => p.ReadIOPS.HasValue).Min(p => p.ReadIOPS!.Value),
                Max = performanceResults.Where(p => p.ReadIOPS.HasValue).Max(p => p.ReadIOPS!.Value)
            },
            WriteIOPS = new
            {
                Average = performanceResults.Where(p => p.WriteIOPS.HasValue).Average(p => p.WriteIOPS!.Value),
                Min = performanceResults.Where(p => p.WriteIOPS.HasValue).Min(p => p.WriteIOPS!.Value),
                Max = performanceResults.Where(p => p.WriteIOPS.HasValue).Max(p => p.WriteIOPS!.Value)
            }
        };
    }

    /// <summary>
    /// 生成简单的JSON报告（当数据提取失败时使用）
    /// </summary>
    /// <returns>简单JSON报告</returns>
    private async Task<string> GenerateSimpleJsonReportAsync()
    {
        await Task.CompletedTask;
        
        var simpleReport = new
        {
            ReportMetadata = new
            {
                Title = "磁盘稳定性测试报告",
                GeneratedAt = DateTime.Now,
                GeneratedBy = GeneratorName,
                Status = "Error",
                Message = "数据提取失败 - 使用简化格式"
            }
        };

        return JsonSerializer.Serialize(simpleReport, _jsonOptions);
    }
}
