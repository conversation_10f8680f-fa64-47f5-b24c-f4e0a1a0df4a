using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Enums;
using MassStorageStableTestTool.Reports.Common;
using MassStorageStableTestTool.Reports.Models;
using Microsoft.Extensions.Logging;
using Scriban;
using System.Text;

namespace MassStorageStableTestTool.Reports.Generators;

/// <summary>
/// HTML格式报告生成器
/// </summary>
public class HtmlReportGenerator : BaseReportGenerator
{
    /// <summary>
    /// 支持的报告格式
    /// </summary>
    public override ReportFormat SupportedFormat => ReportFormat.HTML;

    /// <summary>
    /// 生成器名称
    /// </summary>
    public override string GeneratorName => "HTML Report Generator";

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="configuration">报告配置</param>
    public HtmlReportGenerator(ILogger<HtmlReportGenerator> logger, ReportGenerationConfiguration? configuration = null)
        : base(logger, configuration)
    {
    }

    /// <summary>
    /// 生成报告内容
    /// </summary>
    /// <param name="reportData">报告数据</param>
    /// <param name="template">模板内容</param>
    /// <returns>报告内容</returns>
    protected override async Task<string> GenerateReportContentAsync(object reportData, string template)
    {
        try
        {
            var scriptTemplate = Template.Parse(template);
            var result = await scriptTemplate.RenderAsync(reportData);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "使用模板生成HTML报告时出错");
            return GenerateSimpleHtmlReport(reportData);
        }
    }

    /// <summary>
    /// 获取默认文件扩展名
    /// </summary>
    /// <returns>文件扩展名</returns>
    public override string GetDefaultFileExtension() => ".html";

    /// <summary>
    /// 验证报告内容
    /// </summary>
    /// <param name="reportContent">报告内容</param>
    /// <returns>错误列表</returns>
    protected override List<string> ValidateReportContent(string reportContent)
    {
        var errors = new List<string>();

        if (!reportContent.Contains("<html", StringComparison.OrdinalIgnoreCase))
        {
            errors.Add("HTML报告缺少<html>标签");
        }

        if (!reportContent.Contains("<head", StringComparison.OrdinalIgnoreCase))
        {
            errors.Add("HTML报告缺少<head>标签");
        }

        if (!reportContent.Contains("<body", StringComparison.OrdinalIgnoreCase))
        {
            errors.Add("HTML报告缺少<body>标签");
        }

        return errors;
    }

    /// <summary>
    /// 获取后备模板
    /// </summary>
    /// <returns>后备模板内容</returns>
    protected override string GetFallbackTemplate()
    {
        return @"
<!DOCTYPE html>
<html lang=""zh-CN"">
<head>
    <meta charset=""UTF-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <title>{{ configuration.title }}</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #007acc;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #007acc;
            margin: 0;
            font-size: 2.5em;
        }
        .meta-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #333;
            border-bottom: 2px solid #007acc;
            padding-bottom: 10px;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .summary-card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            text-align: center;
            border-left: 4px solid #007acc;
        }
        .summary-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .summary-card .value {
            font-size: 2em;
            font-weight: bold;
            color: #007acc;
        }
        .test-result {
            background-color: #f8f9fa;
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 5px;
            border-left: 4px solid #28a745;
        }
        .test-result.failed {
            border-left-color: #dc3545;
        }
        .test-result h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        .performance-data {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .performance-item {
            background-color: white;
            padding: 10px;
            border-radius: 3px;
            text-align: center;
        }
        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
        .warning-list {
            background-color: #fff3cd;
            color: #856404;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
        }
        .system-info {
            background-color: #e9ecef;
            padding: 20px;
            border-radius: 5px;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
        }
        {{ if configuration.custom_css }}
        {{ configuration.custom_css }}
        {{ end }}
    </style>
</head>
<body>
    <div class=""container"">
        <div class=""header"">
            <h1>{{ configuration.title }}</h1>
        </div>

        <div class=""meta-info"">
            <p><strong>报告生成时间:</strong> {{ generated_at | date: 'yyyy-MM-dd HH:mm:ss' }}</p>
            <p><strong>生成器版本:</strong> {{ version }}</p>
            <p><strong>报告作者:</strong> {{ configuration.author }}</p>
        </div>

        <div class=""section"">
            <h2>测试概览</h2>
            <div class=""summary-grid"">
                <div class=""summary-card"">
                    <h3>测试总数</h3>
                    <div class=""value"">{{ test_suite.total_tests_count }}</div>
                </div>
                <div class=""summary-card"">
                    <h3>成功测试</h3>
                    <div class=""value"" style=""color: #28a745;"">{{ test_suite.successful_tests_count }}</div>
                </div>
                <div class=""summary-card"">
                    <h3>失败测试</h3>
                    <div class=""value"" style=""color: #dc3545;"">{{ test_suite.failed_tests_count }}</div>
                </div>
                <div class=""summary-card"">
                    <h3>成功率</h3>
                    <div class=""value"">{{ test_suite.success_rate | math.round: 1 }}%</div>
                </div>
                <div class=""summary-card"">
                    <h3>总耗时</h3>
                    <div class=""value"">{{ test_suite.duration.total_minutes | math.round: 1 }}分钟</div>
                </div>
            </div>
            
            <p><strong>目标驱动器:</strong> {{ test_suite.configuration.target_drive }}</p>
            <p><strong>测试开始时间:</strong> {{ test_suite.start_time | date: 'yyyy-MM-dd HH:mm:ss' }}</p>
            <p><strong>测试结束时间:</strong> {{ test_suite.end_time | date: 'yyyy-MM-dd HH:mm:ss' }}</p>
            <p><strong>整体状态:</strong> {{ test_suite.status }}</p>
        </div>

        <div class=""section"">
            <h2>详细测试结果</h2>
            {{ for test_result in test_suite.test_results }}
            <div class=""test-result {{ if test_result.success }}{{ else }}failed{{ end }}"">
                <h3>{{ test_result.tool_name }}</h3>
                <p><strong>状态:</strong> {{ test_result.status }}</p>
                <p><strong>结果:</strong> {{ if test_result.success }}✅ 成功{{ else }}❌ 失败{{ end }}</p>
                <p><strong>开始时间:</strong> {{ test_result.start_time | date: 'yyyy-MM-dd HH:mm:ss' }}</p>
                <p><strong>结束时间:</strong> {{ test_result.end_time | date: 'yyyy-MM-dd HH:mm:ss' }}</p>
                <p><strong>耗时:</strong> {{ test_result.duration.total_minutes | math.round: 2 }} 分钟</p>

                {{ if test_result.performance }}
                <div class=""performance-data"">
                    {{ if test_result.performance.read_speed }}
                    <div class=""performance-item"">
                        <strong>读取速度</strong><br>
                        {{ test_result.performance.read_speed | math.round: 2 }} MB/s
                    </div>
                    {{ end }}
                    {{ if test_result.performance.write_speed }}
                    <div class=""performance-item"">
                        <strong>写入速度</strong><br>
                        {{ test_result.performance.write_speed | math.round: 2 }} MB/s
                    </div>
                    {{ end }}
                    {{ if test_result.performance.read_iops }}
                    <div class=""performance-item"">
                        <strong>读取IOPS</strong><br>
                        {{ test_result.performance.read_iops | math.round: 0 }}
                    </div>
                    {{ end }}
                    {{ if test_result.performance.write_iops }}
                    <div class=""performance-item"">
                        <strong>写入IOPS</strong><br>
                        {{ test_result.performance.write_iops | math.round: 0 }}
                    </div>
                    {{ end }}
                </div>
                {{ end }}

                {{ if test_result.error_message }}
                <div class=""error-message"">
                    <strong>错误信息:</strong> {{ test_result.error_message }}
                </div>
                {{ end }}

                {{ if test_result.warnings.size > 0 }}
                <div class=""warning-list"">
                    <strong>警告信息:</strong>
                    <ul>
                    {{ for warning in test_result.warnings }}
                        <li>{{ warning }}</li>
                    {{ end }}
                    </ul>
                </div>
                {{ end }}
            </div>
            {{ end }}
        </div>

        {{ if test_suite.system_info || test_suite.drive_info }}
        <div class=""section"">
            <h2>系统信息</h2>
            <div class=""system-info"">
                {{ if test_suite.system_info }}
                <p><strong>操作系统:</strong> {{ test_suite.system_info.operating_system }}</p>
                <p><strong>处理器:</strong> {{ test_suite.system_info.processor }}</p>
                <p><strong>内存:</strong> {{ test_suite.system_info.total_memory_gb | math.round: 2 }} GB</p>
                {{ end }}
                
                {{ if test_suite.drive_info }}
                <hr>
                <p><strong>驱动器:</strong> {{ test_suite.drive_info.name }}</p>
                <p><strong>标签:</strong> {{ test_suite.drive_info.label }}</p>
                <p><strong>文件系统:</strong> {{ test_suite.drive_info.file_system }}</p>
                <p><strong>总容量:</strong> {{ test_suite.drive_info.total_size_gb | math.round: 2 }} GB</p>
                <p><strong>可用空间:</strong> {{ test_suite.drive_info.available_free_space_gb | math.round: 2 }} GB</p>
                {{ end }}
            </div>
        </div>
        {{ end }}

        <div class=""footer"">
            <p>报告由 {{ generated_by }} 生成 | 版本 {{ version }}</p>
        </div>
    </div>

    {{ if configuration.custom_java_script }}
    <script>
    {{ configuration.custom_java_script }}
    </script>
    {{ end }}
</body>
</html>
";
    }

    /// <summary>
    /// 生成简单的HTML报告（当模板解析失败时使用）
    /// </summary>
    /// <param name="reportData">报告数据</param>
    /// <returns>简单HTML报告</returns>
    private string GenerateSimpleHtmlReport(object reportData)
    {
        var sb = new StringBuilder();
        
        sb.AppendLine("<!DOCTYPE html>");
        sb.AppendLine("<html lang=\"zh-CN\">");
        sb.AppendLine("<head>");
        sb.AppendLine("    <meta charset=\"UTF-8\">");
        sb.AppendLine("    <title>磁盘稳定性测试报告</title>");
        sb.AppendLine("    <style>");
        sb.AppendLine("        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; }");
        sb.AppendLine("        .error { color: red; background-color: #ffe6e6; padding: 10px; border-radius: 5px; }");
        sb.AppendLine("    </style>");
        sb.AppendLine("</head>");
        sb.AppendLine("<body>");
        sb.AppendLine("    <h1>磁盘稳定性测试报告</h1>");
        sb.AppendLine("    <div class=\"error\">");
        sb.AppendLine("        <p><strong>注意:</strong> 由于模板解析失败，此报告使用简化格式生成。</p>");
        sb.AppendLine("    </div>");
        sb.AppendLine($"    <p>报告生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}</p>");
        sb.AppendLine($"    <p>生成器: {GeneratorName}</p>");
        sb.AppendLine("</body>");
        sb.AppendLine("</html>");
        
        return sb.ToString();
    }
}
